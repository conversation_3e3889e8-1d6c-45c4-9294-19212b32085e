---
// Footer component with Tailwind styling
interface Props {
  lang?: string;
  translations?: any;
}

const { lang = 'sq', translations } = Astro.props;

// Get translation function
function t(key) {
  if (!translations) return key;
  const keys = key.split('.');
  let value = translations;
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      return key;
    }
  }
  return value;
}
---

<footer class="bg-gray-900 text-white">
  <div class="mx-auto px-4 sm:px-6 lg:px-8 py-12 max-w-7xl">
    <div class="items-center gap-8 grid grid-cols-1 md:grid-cols-2">
      <!-- Logo and Brand -->
      <div class="flex items-center space-x-3">
        <img
          src={`${import.meta.env.BASE_URL}bar-joni-logo.jpg`}
          alt="Bar Joni Logo"
          width="48"
          height="48"
          class="rounded-full w-12 h-12 object-cover"
        />
        <span class="font-bold text-xl">{t('navigation.brand')}</span>
      </div>

      <!-- Description and Copyright -->
      <div class="space-y-2 text-center md:text-right">
        <p class="text-gray-300">
          {t('footer.description')}
        </p>
        <p class="text-gray-400">
          {t('footer.copyright')}
        </p>
      </div>
    </div>
  </div>
</footer>
